
import { useState } from 'react';
import { ArrowUpRight, Code2, CheckCircle, Clock, Rocket, Calendar, Share2, ExternalLink } from 'lucide-react';
import GlassCard from './GlassCard';

const ProjectsSection = () => {
  const [hoveredProject, setHoveredProject] = useState<number | null>(null);
  const [shareStatus, setShareStatus] = useState<{ [key: number]: string }>({});

  // Status icon mapping
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Live Production':
        return { icon: CheckCircle, color: 'text-green-400', bgColor: 'bg-green-500/10', borderColor: 'border-green-500/20' };
      case 'In Development':
        return { icon: Clock, color: 'text-orange-400', bgColor: 'bg-orange-500/10', borderColor: 'border-orange-500/20' };
      case 'Deployed':
        return { icon: Rocket, color: 'text-blue-400', bgColor: 'bg-blue-500/10', borderColor: 'border-blue-500/20' };
      case 'Planned':
        return { icon: Calendar, color: 'text-purple-400', bgColor: 'bg-purple-500/10', borderColor: 'border-purple-500/20' };
      default:
        return { icon: CheckCircle, color: 'text-green-400', bgColor: 'bg-green-500/10', borderColor: 'border-green-500/20' };
    }
  };

  // Share functionality
  const shareProject = async (project: any) => {
    const shareData = {
      title: `${project.title} - ${project.subtitle}`,
      text: project.description,
      url: `${window.location.origin}#project-${project.id}`
    };

    try {
      if (navigator.share && navigator.canShare && navigator.canShare(shareData)) {
        await navigator.share(shareData);
        setShareStatus({ ...shareStatus, [project.id]: 'Shared successfully!' });
      } else {
        // Fallback: Copy to clipboard
        await navigator.clipboard.writeText(`${shareData.title}\n${shareData.text}\n${shareData.url}`);
        setShareStatus({ ...shareStatus, [project.id]: 'Copied to clipboard!' });
      }
    } catch (error) {
      // Fallback: Copy to clipboard
      try {
        await navigator.clipboard.writeText(`${shareData.title}\n${shareData.text}\n${shareData.url}`);
        setShareStatus({ ...shareStatus, [project.id]: 'Copied to clipboard!' });
      } catch (clipboardError) {
        setShareStatus({ ...shareStatus, [project.id]: 'Share failed' });
      }
    }

    // Clear status after 2 seconds
    setTimeout(() => {
      setShareStatus({ ...shareStatus, [project.id]: '' });
    }, 2000);
  };

  const projects = [
    {
      id: 1,
      title: "Oracle Integral",
      subtitle: "Enterprise Platform",
      description: "Advanced integration platform with real-time analytics, serving Fortune 500 companies with mission-critical operations.",
      technologies: ["React", "Node.js", "Oracle", "Docker", "Kubernetes"],
      year: "2024",
      status: "Live Production"
    },
    {
      id: 2,
      title: "Neural Analytics",
      subtitle: "AI/ML Platform",
      description: "Intelligent data visualization system with machine learning capabilities for predictive business insights.",
      technologies: ["TypeScript", "Python", "TensorFlow", "PostgreSQL", "AWS"],
      year: "2024",
      status: "In Development"
    },
    {
      id: 3,
      title: "Quantum Dashboard",
      subtitle: "Data Visualization",
      description: "Next-generation business intelligence platform with real-time data processing and advanced analytics.",
      technologies: ["React", "D3.js", "FastAPI", "Redis", "Docker"],
      year: "2023",
      status: "Deployed"
    },
    {
      id: 4,
      title: "Nexus Framework",
      subtitle: "Development Tools",
      description: "Comprehensive development framework for rapid prototyping and scalable application architecture.",
      technologies: ["TypeScript", "Vite", "Tailwind", "Supabase", "Vercel"],
      year: "2025",
      status: "Planned"
    }
  ];

  return (
    <section className="py-32 px-6 bg-gradient-to-b from-slate-900 to-slate-950">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <div className="flex flex-col lg:flex-row lg:items-end lg:justify-between mb-20">
          <div className="max-w-2xl">
            <div className="flex items-center gap-4 mb-6">
              <div className="w-12 h-px bg-gradient-to-r from-white/30 to-transparent" />
              <span className="text-sm font-light text-white/60 tracking-[0.2em] uppercase">Portfolio</span>
            </div>
            <h3 className="text-4xl md:text-6xl font-extralight text-white mb-6">
              Featured Projects
            </h3>
            <p className="text-xl text-slate-400 font-light">
              A curated selection of work that demonstrates technical excellence and innovative problem-solving.
            </p>
          </div>
          
          <button className="mt-8 lg:mt-0 inline-flex items-center gap-2 text-white/60 hover:text-white transition-colors duration-300 group">
            <span className="text-sm font-light">View Archive</span>
            <ArrowUpRight className="w-4 h-4 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-300" />
          </button>
        </div>

        {/* Projects List */}
        <div className="space-y-8">
          {projects.map((project, index) => (
            <div
              key={project.id}
              className="group relative"
              onMouseEnter={() => setHoveredProject(project.id)}
              onMouseLeave={() => setHoveredProject(null)}
            >
              <GlassCard className={`
                transition-all duration-500 cursor-pointer border-white/5 p-8 lg:p-12
                ${hoveredProject === project.id ? 'border-white/10' : ''}
              `}>
                <div className="grid lg:grid-cols-3 gap-8 items-center">
                  {/* Project Info */}
                  <div className="lg:col-span-2">
                    <div className="flex items-center gap-4 mb-4">
                      <span className="text-6xl lg:text-8xl font-extralight text-white/10 group-hover:text-white/20 transition-colors duration-500">
                        {String(index + 1).padStart(2, '0')}
                      </span>
                      <div>
                        <h4 className="text-2xl lg:text-3xl font-light text-white group-hover:text-white/90 transition-colors duration-300">
                          {project.title}
                        </h4>
                        <p className="text-slate-400 font-light">{project.subtitle}</p>
                      </div>
                    </div>
                    
                    <p className="text-slate-400 leading-relaxed font-light mb-6 max-w-2xl">
                      {project.description}
                    </p>

                    {/* Technologies */}
                    <div className="flex flex-wrap gap-2 mb-6">
                      {project.technologies.map((tech, techIndex) => (
                        <span
                          key={techIndex}
                          className="px-3 py-1 bg-white/5 text-white/60 text-xs rounded-full border border-white/10 font-light"
                        >
                          {tech}
                        </span>
                      ))}
                    </div>

                    {/* Action Links */}
                    <div className="flex gap-6">
                      <button className="flex items-center gap-2 text-white/60 hover:text-white transition-colors duration-300 group">
                        <Code2 className="w-4 h-4" />
                        <span className="text-sm font-light">Source</span>
                        <ArrowUpRight className="w-3 h-3 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-300" />
                      </button>
                      <button className="flex items-center gap-2 text-white/60 hover:text-white transition-colors duration-300 group">
                        <ExternalLink className="w-4 h-4" />
                        <span className="text-sm font-light">Live Demo</span>
                        <ArrowUpRight className="w-3 h-3 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-300" />
                      </button>
                      <button
                        onClick={() => shareProject(project)}
                        className="flex items-center gap-2 text-white/60 hover:text-white transition-colors duration-300 group relative"
                      >
                        <Share2 className="w-4 h-4" />
                        <span className="text-sm font-light">Share</span>
                        {shareStatus[project.id] && (
                          <div className="absolute -top-8 left-0 bg-white/10 text-white text-xs px-2 py-1 rounded backdrop-blur-xl border border-white/20">
                            {shareStatus[project.id]}
                          </div>
                        )}
                      </button>
                    </div>
                  </div>

                  {/* Project Meta */}
                  <div className="text-right">
                    <div className="text-2xl font-light text-white mb-2">{project.year}</div>
                    {(() => {
                      const statusConfig = getStatusIcon(project.status);
                      const StatusIcon = statusConfig.icon;
                      return (
                        <div className={`inline-flex items-center gap-2 px-3 py-1 ${statusConfig.bgColor} ${statusConfig.color} text-xs rounded-full border ${statusConfig.borderColor}`}>
                          <StatusIcon className="w-3 h-3" />
                          {project.status}
                        </div>
                      );
                    })()}
                  </div>
                </div>
              </GlassCard>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ProjectsSection;
