
import { Code2, <PERSON>, Users } from 'lucide-react';
import GlassCard from './GlassCard';

const ServicesSection = () => {
  const services = [
    {
      icon: Code2,
      title: "Software Development",
      description: "Full-stack applications with modern architectures and scalable solutions for enterprise needs.",
      capabilities: ["Frontend Development", "Backend Architecture", "Database Design", "DevOps & Deployment"]
    },
    {
      icon: Brain,
      title: "AI Integration",
      description: "Intelligent systems leveraging machine learning and AI to solve complex business challenges.",
      capabilities: ["Machine Learning", "Data Analytics", "Automation", "Predictive Modeling"]
    },
    {
      icon: Users,
      title: "Technical Consulting",
      description: "Strategic guidance for technology decisions, architecture planning, and team development.",
      capabilities: ["Architecture Review", "Technology Strategy", "Code Audits", "Team Leadership"]
    }
  ];

  return (
    <section className="pb-16 px-6 bg-gradient-to-b from-slate-950 to-slate-900">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-12">
          <div className="w-16 h-px bg-gradient-to-r from-transparent via-white/30 to-transparent mx-auto mb-8" />
          <span className="text-sm font-light text-white/60 tracking-[0.3em] uppercase mb-8 block">Services</span>
          <h3 className="text-4xl md:text-6xl font-extralight text-white mb-6">
            How I Can Help
          </h3>
          <p className="text-xl text-slate-400 max-w-3xl mx-auto font-light">
            Combining deep technical expertise with strategic thinking to deliver impactful solutions
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <GlassCard
              key={index}
              className="group h-full cursor-pointer border-white/5 hover:border-white/10 transition-all duration-500"
            >
              {/* Service Icon */}
              <div className="mb-8">
                <service.icon className="w-8 h-8 text-white/70 group-hover:text-white transition-colors duration-300" />
              </div>

              {/* Service Title */}
              <h4 className="text-xl font-light text-white mb-4 group-hover:text-white/90 transition-colors duration-300">
                {service.title}
              </h4>

              {/* Service Description */}
              <p className="text-slate-400 mb-8 leading-relaxed font-light">
                {service.description}
              </p>

              {/* Service Capabilities */}
              <ul className="space-y-3">
                {service.capabilities.map((capability, capIndex) => (
                  <li key={capIndex} className="flex items-center text-slate-400">
                    <div className="w-1 h-1 bg-white/40 rounded-full mr-3" />
                    <span className="text-sm font-light">{capability}</span>
                  </li>
                ))}
              </ul>
            </GlassCard>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;
