import { useState, useEffect, useCallback, useRef } from 'react';
import { useIsMobile } from '@/hooks/use-mobile';
import DesktopNavigation from './DesktopNavigation';
import MobileNavigation from './MobileNavigation';

const NavigationContainer = () => {
  const isMobile = useIsMobile();
  const [activeSection, setActiveSection] = useState('Home');
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Debounced section update to prevent flickering
  const debouncedSetActiveSection = useCallback((sectionName: string) => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    debounceTimeoutRef.current = setTimeout(() => {
      setActiveSection(sectionName);
    }, 50);
  }, []);

  // Track active section based on scroll position
  useEffect(() => {
    const sections = [
      { id: 'hero', name: 'Home' },
      { id: 'about', name: 'About' },
      { id: 'projects', name: 'Portfolio' },
      { id: 'services', name: 'Services' },
      { id: 'contact', name: 'Contact' }
    ];

    const observerOptions = {
      root: null,
      rootMargin: '-20% 0px -60% 0px',
      threshold: [0, 0.25, 0.5, 0.75, 1]
    };

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      // Sort entries by intersection ratio (highest first)
      const sortedEntries = entries
        .filter(entry => entry.isIntersecting)
        .sort((a, b) => b.intersectionRatio - a.intersectionRatio);

      if (sortedEntries.length > 0) {
        const mostVisibleEntry = sortedEntries[0];
        let sectionName: string | undefined;

        // Check if it's the hero section
        if (mostVisibleEntry.target.classList.contains('min-h-screen') ||
            mostVisibleEntry.target.className.includes('min-h-screen')) {
          sectionName = 'Home';
        } else {
          // Find section by ID
          sectionName = sections.find(section => section.id === mostVisibleEntry.target.id)?.name;
        }

        if (sectionName) {
          debouncedSetActiveSection(sectionName);
        }
      }
    };

    const observer = new IntersectionObserver(observerCallback, observerOptions);

    // Observe sections
    sections.forEach(({ id }) => {
      let element: Element | null;
      if (id === 'hero') {
        // Find the hero section by looking for the section with min-h-screen class
        element = document.querySelector('section.min-h-screen, section[class*="min-h-screen"]');
      } else {
        element = document.getElementById(id);
      }
      if (element) {
        observer.observe(element);
      }
    });

    // Handle initial load and hash changes
    const handleHashChange = () => {
      const hash = window.location.hash.slice(1);
      const section = sections.find(s => s.id === hash);
      if (section) {
        setActiveSection(section.name);
      } else if (!hash) {
        setActiveSection('Home');
      }
    };

    handleHashChange();
    window.addEventListener('hashchange', handleHashChange);

    return () => {
      observer.disconnect();
      window.removeEventListener('hashchange', handleHashChange);
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, [debouncedSetActiveSection]);

  const navItems = [
    { name: 'Home', href: '#' },
    { name: 'About', href: '#about' },
    { name: 'Portfolio', href: '#projects' },
    { name: 'Services', href: '#services' },
    { name: 'Contact', href: '#contact' }
  ];

  // Conditional rendering based on screen size
  if (isMobile) {
    return (
      <MobileNavigation 
        activeSection={activeSection}
        navItems={navItems}
      />
    );
  }

  return (
    <DesktopNavigation 
      activeSection={activeSection}
      navItems={navItems}
    />
  );
};

export default NavigationContainer;
