import { useEffect, useRef, useState } from 'react';

interface NavItem {
  name: string;
  href: string;
}

interface MobileNavigationOverlayProps {
  isOpen: boolean;
  onClose: () => void;
  activeSection: string;
  navItems: NavItem[];
}

const MobileNavigationOverlay = ({
  isOpen,
  onClose,
  activeSection,
  navItems
}: MobileNavigationOverlayProps) => {
  const dropdownRef = useRef<HTMLDivElement>(null);
  const backdropRef = useRef<HTMLDivElement>(null);
  const firstFocusableRef = useRef<HTMLButtonElement>(null);
  const navItemRefs = useRef<(HTMLButtonElement | null)[]>([]);
  const [indicatorStyle, setIndicatorStyle] = useState({ top: 0, height: 0, opacity: 0 });

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      // Focus the first focusable element when menu opens
      setTimeout(() => {
        firstFocusableRef.current?.focus();
      }, 150);
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose]);

  // Handle click outside to close
  useEffect(() => {
    if (!isOpen) return;

    const handleClickOutside = (e: MouseEvent | TouchEvent) => {
      const target = e.target as Node;

      // Check if click is outside the dropdown but not on the hamburger button
      if (dropdownRef.current && !dropdownRef.current.contains(target)) {
        // Also check if it's not the hamburger button (which has its own handler)
        const hamburgerButton = document.querySelector('[aria-label="Open menu"], [aria-label="Close menu"]');
        if (!hamburgerButton?.contains(target)) {
          onClose();
        }
      }
    };

    // Add event listeners for both mouse and touch events
    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('touchstart', handleClickOutside, { passive: true });

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('touchstart', handleClickOutside);
    };
  }, [isOpen, onClose]);

  // Update indicator position when active section changes
  useEffect(() => {
    if (!isOpen) return;

    const updateIndicatorPosition = () => {
      const activeIndex = navItems.findIndex(item => item.name === activeSection);
      const activeElement = navItemRefs.current[activeIndex];
      const container = dropdownRef.current?.querySelector('.space-y-2');

      if (activeElement && container) {
        const containerRect = container.getBoundingClientRect();
        const elementRect = activeElement.getBoundingClientRect();

        const top = elementRect.top - containerRect.top;
        const height = elementRect.height;

        setIndicatorStyle({
          top,
          height,
          opacity: 1
        });
      } else {
        setIndicatorStyle(prev => ({ ...prev, opacity: 0 }));
      }
    };

    // Delay to ensure animations are complete
    const timeoutId = setTimeout(updateIndicatorPosition, 100);

    return () => clearTimeout(timeoutId);
  }, [activeSection, navItems, isOpen]);

  const handleNavClick = (href: string) => {
    onClose();
    // Small delay to allow menu to close before navigation
    setTimeout(() => {
      if (href === '#') {
        window.scrollTo({ top: 0, behavior: 'smooth' });
      } else {
        const element = document.querySelector(href);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' });
        }
      }
    }, 200);
  };

  return (
    <>
      {/* Backdrop */}
      {isOpen && (
        <div
          ref={backdropRef}
          className="mobile-nav-backdrop fixed inset-0 z-[9998] bg-black/20 transition-opacity duration-300"
        />
      )}

      {/* Dropdown Container */}
      <div
        ref={dropdownRef}
        className={`
          mobile-nav-dropdown fixed left-4 right-4 z-[9999] mt-2
          bg-gradient-to-br from-black/90 via-black/80 to-black/70
          backdrop-blur-2xl border border-white/10 rounded-2xl
          shadow-2xl shadow-black/50
          transform-gpu transition-all duration-300 ease-out origin-top
          ${isOpen
            ? 'opacity-100 scale-y-100 translate-y-0'
            : 'opacity-0 scale-y-95 -translate-y-2 pointer-events-none'
          }
        `}
        style={{
          top: 'calc(var(--nav-height, 80px) + 0.5rem)',
        }}
      >

        {/* Navigation Items */}
        <div className="p-6 space-y-2 relative">
          {/* Animated Background Indicator */}
          <div
            className="nav-indicator absolute left-6 right-6 bg-gradient-to-r from-orange-500/30 to-orange-400/20 rounded-xl transition-all duration-250 ease-in-out border border-orange-500/20 shadow-lg shadow-orange-500/10"
            style={{
              top: `${indicatorStyle.top}px`,
              height: `${indicatorStyle.height}px`,
              opacity: indicatorStyle.opacity,
              transform: 'translateZ(0)', // Hardware acceleration
            }}
          />

          {navItems.map((item, index) => (
            <button
              key={item.name}
              ref={(el) => {
                navItemRefs.current[index] = el;
                if (index === 0) firstFocusableRef.current = el;
              }}
              onClick={() => handleNavClick(item.href)}
              className={`
                mobile-nav-item nav-item relative z-10 w-full text-left px-6 py-4 rounded-xl text-lg font-medium
                transition-all duration-250 ease-in-out min-h-[56px] flex items-center
                ${activeSection === item.name
                  ? 'text-white font-semibold'
                  : 'text-white/70 hover:text-white hover:bg-white/5'
                }
                hover:scale-[1.01] hover:shadow-lg hover:shadow-orange-500/10
                focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:ring-offset-2 focus:ring-offset-black/50
                transform translate-y-2 opacity-0
              `}
              style={{
                animationDelay: `${index * 80}ms`,
                animation: isOpen ? `mobile-nav-item-enter 0.4s ease-out ${index * 80}ms both` : 'none'
              }}
              aria-current={activeSection === item.name ? 'page' : undefined}
            >
              {item.name}
            </button>
          ))}

          {/* CTA Button */}
          <button
            onClick={() => handleNavClick('#contact')}
            className="w-full mt-4 px-6 py-4 rounded-xl bg-gradient-to-r from-white to-orange-50 text-black font-semibold text-lg
                     hover:from-orange-50 hover:to-orange-100 transition-all duration-200
                     min-h-[56px] flex items-center justify-center
                     hover:scale-[1.01] hover:shadow-lg hover:shadow-orange-500/20
                     focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:ring-offset-2 focus:ring-offset-black/50
                     transform translate-y-2 opacity-0"
            style={{
              animationDelay: `${navItems.length * 80}ms`,
              animation: isOpen ? `mobile-nav-item-enter 0.4s ease-out ${navItems.length * 80}ms both` : 'none'
            }}
          >
            Get Started
          </button>
        </div>
      </div>
    </>
  );
};

export default MobileNavigationOverlay;
