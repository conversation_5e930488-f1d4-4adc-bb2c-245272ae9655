import { useState, useEffect, useRef } from 'react';

interface NavItem {
  name: string;
  href: string;
}

interface DesktopNavigationProps {
  activeSection: string;
  navItems: NavItem[];
}

const DesktopNavigation = ({ activeSection, navItems }: DesktopNavigationProps) => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [indicatorStyle, setIndicatorStyle] = useState({ left: 0, width: 0, opacity: 0 });
  const navContainerRef = useRef<HTMLDivElement>(null);
  const navItemRefs = useRef<(HTMLAnchorElement | null)[]>([]);

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      setIsScrolled(currentScrollY > 20);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Update indicator position when active section changes
  useEffect(() => {
    const updateIndicatorPosition = () => {
      const activeIndex = navItems.findIndex(item => item.name === activeSection);
      const activeElement = navItemRefs.current[activeIndex];
      const container = navContainerRef.current;

      if (activeElement && container) {
        const containerRect = container.getBoundingClientRect();
        const elementRect = activeElement.getBoundingClientRect();

        const left = elementRect.left - containerRect.left;
        const width = elementRect.width;

        setIndicatorStyle({
          left,
          width,
          opacity: 1
        });
      } else {
        setIndicatorStyle(prev => ({ ...prev, opacity: 0 }));
      }
    };

    // Small delay to ensure DOM is updated
    const timeoutId = setTimeout(updateIndicatorPosition, 25);

    // Also update on resize
    const handleResize = () => {
      setTimeout(updateIndicatorPosition, 25);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener('resize', handleResize);
    };
  }, [activeSection, navItems]);

  const handleNavClick = (href: string, e: React.MouseEvent) => {
    e.preventDefault();

    if (href === '#') {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    } else {
      const element = document.querySelector(href);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    }
  };

  return (
    <nav className={`
      fixed top-0 left-0 right-0 z-[9999] transition-all duration-300 px-3 sm:px-6
      ${isScrolled ? 'py-2 sm:py-3' : 'py-4 sm:py-6'}
    `}>
      <div className="max-w-7xl mx-auto">
        <div className={`
          transition-all duration-300 rounded-full border
          ${isScrolled 
            ? 'bg-black/60 border-white/15 shadow-lg shadow-black/20' 
            : 'bg-black/20 border-white/5'
          }
          backdrop-blur-xl px-4 sm:px-6 py-2 sm:py-3
          hover-glow
        `}>
          <div className="flex items-center justify-between">
            {/* Logo */}
            <a href="#" className="flex items-center gap-2 sm:gap-3">
              <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-gradient-to-br from-white via-orange-100 to-orange-200 flex items-center justify-center">
                <span className="text-black text-xs sm:text-sm font-semibold">B</span>
              </div>
              <span className="text-white font-medium text-base sm:text-lg hidden sm:block">
                Baraa Al Khateeb
              </span>
            </a>

            {/* Desktop Navigation - Pill Style */}
            <div className="flex items-center">
              <div
                ref={navContainerRef}
                className="relative flex items-center bg-white/5 rounded-full p-1 border border-white/10"
              >
                {/* Animated Background Indicator */}
                <div
                  className="nav-indicator absolute top-1 bottom-1 bg-gradient-to-r from-orange-500/30 to-orange-400/20 rounded-full transition-all duration-250 ease-in-out border border-orange-500/20 shadow-lg shadow-orange-500/10"
                  style={{
                    left: `${indicatorStyle.left}px`,
                    width: `${indicatorStyle.width}px`,
                    opacity: indicatorStyle.opacity,
                    transform: 'translateZ(0)', // Hardware acceleration
                  }}
                />

                {navItems.map((item, index) => (
                  <a
                    key={item.name}
                    ref={(el) => (navItemRefs.current[index] = el)}
                    href={item.href}
                    onClick={(e) => handleNavClick(item.href, e)}
                    className={`
                      nav-item relative z-10 px-3 xl:px-4 py-2 rounded-full text-sm font-medium transition-all duration-250 ease-in-out
                      hover:text-white hover-lift focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:ring-offset-2 focus:ring-offset-black/50
                      ${activeSection === item.name
                        ? 'text-white font-semibold'
                        : 'text-white/70 hover:bg-white/5'
                      }
                    `}
                    aria-current={activeSection === item.name ? 'page' : undefined}
                  >
                    {item.name}
                  </a>
                ))}
              </div>
              
              {/* CTA Button */}
              <button className="ml-3 xl:ml-4 px-4 xl:px-6 py-2 rounded-full bg-gradient-to-r from-white to-orange-50 text-black font-medium hover:from-orange-50 hover:to-orange-100 transition-all duration-150 text-sm hover-lift hover-glow">
                Get Started
              </button>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default DesktopNavigation;
