import { useEffect, useState } from 'react';
import { Code2, Sparkles } from 'lucide-react';

interface PreloaderProps {
  onComplete: () => void;
}

const Preloader = ({ onComplete }: PreloaderProps) => {
  const [progress, setProgress] = useState(0);
  const [loadingText, setLoadingText] = useState('Initializing...');
  const [isComplete, setIsComplete] = useState(false);

  const loadingSteps = [
    { progress: 20, text: 'Loading assets...' },
    { progress: 40, text: 'Preparing interface...' },
    { progress: 60, text: 'Optimizing experience...' },
    { progress: 80, text: 'Finalizing details...' },
    { progress: 100, text: 'Welcome!' }
  ];

  useEffect(() => {
    let currentStep = 0;
    const interval = setInterval(() => {
      if (currentStep < loadingSteps.length) {
        const step = loadingSteps[currentStep];
        setProgress(step.progress);
        setLoadingText(step.text);
        currentStep++;
      } else {
        clearInterval(interval);
        setIsComplete(true);
        setTimeout(() => {
          onComplete();
        }, 800);
      }
    }, 600);

    return () => clearInterval(interval);
  }, [onComplete]);

  return (
    <div className={`fixed inset-0 z-50 bg-gradient-to-br from-black via-slate-950 to-black flex items-center justify-center transition-opacity duration-1000 ${isComplete ? 'opacity-0' : 'opacity-100'}`}>
      {/* Background Effects */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Animated Grid */}
        <div className="absolute inset-0 opacity-[0.02]">
          <div 
            className="absolute inset-0 animate-pulse" 
            style={{
              backgroundImage: `
                linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px)
              `,
              backgroundSize: '60px 60px'
            }} 
          />
        </div>

        {/* Floating Particles */}
        <div className="absolute inset-0">
          {[...Array(20)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-orange-400/30 rounded-full animate-float"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 3}s`,
                animationDuration: `${3 + Math.random() * 2}s`
              }}
            />
          ))}
        </div>

        {/* Ambient Glow */}
        <div className="absolute top-1/3 left-1/3 w-96 h-96 bg-gradient-to-r from-orange-500/[0.03] to-transparent rounded-full filter blur-3xl animate-pulse" />
        <div className="absolute bottom-1/3 right-1/3 w-80 h-80 bg-gradient-to-l from-blue-500/[0.02] to-transparent rounded-full filter blur-3xl animate-pulse" />
      </div>

      {/* Main Content */}
      <div className="relative z-10 text-center px-6 max-w-md mx-auto">
        {/* Logo/Brand */}
        <div className="mb-12">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-white/5 border border-white/10 rounded-2xl backdrop-blur-xl mb-6 group">
            <div className="relative">
              <Code2 className="w-8 h-8 text-orange-400 animate-pulse" />
              <Sparkles className="w-4 h-4 text-white/60 absolute -top-1 -right-1 animate-bounce" />
            </div>
          </div>
          <h1 className="text-2xl font-light text-white mb-2">
            Baraa Al Khateeb
          </h1>
          <p className="text-slate-400 text-sm font-light">
            Digital Excellence
          </p>
        </div>

        {/* Progress Section */}
        <div className="space-y-6">
          {/* Progress Bar */}
          <div className="relative">
            <div className="w-full h-1 bg-white/5 rounded-full overflow-hidden backdrop-blur-xl border border-white/10">
              <div 
                className="h-full bg-gradient-to-r from-orange-400 to-orange-500 rounded-full transition-all duration-500 ease-out relative"
                style={{ width: `${progress}%` }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse" />
              </div>
            </div>
            <div className="flex justify-between mt-2">
              <span className="text-xs text-slate-500 font-light">0%</span>
              <span className="text-xs text-orange-400 font-medium">{progress}%</span>
              <span className="text-xs text-slate-500 font-light">100%</span>
            </div>
          </div>

          {/* Loading Text */}
          <div className="h-6 flex items-center justify-center">
            <p className="text-slate-300 text-sm font-light animate-pulse">
              {loadingText}
            </p>
          </div>

          {/* Loading Dots */}
          <div className="flex justify-center gap-1">
            {[...Array(3)].map((_, i) => (
              <div
                key={i}
                className="w-2 h-2 bg-orange-400/60 rounded-full animate-bounce"
                style={{ animationDelay: `${i * 0.2}s` }}
              />
            ))}
          </div>
        </div>

        {/* Completion Message */}
        {isComplete && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center animate-fade-in">
              <div className="w-16 h-16 bg-gradient-to-r from-orange-400 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
                <Sparkles className="w-8 h-8 text-white" />
              </div>
              <p className="text-white text-lg font-light">Ready to explore!</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Preloader;
