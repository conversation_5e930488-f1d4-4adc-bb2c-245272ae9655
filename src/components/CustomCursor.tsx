
import { useEffect, useState, useRef } from 'react';

const CustomCursor = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);
  const [isClicking, setIsClicking] = useState(false);
  const [trail, setTrail] = useState<Array<{ x: number; y: number; id: number }>>([]);
  const trailIdRef = useRef(0);

  useEffect(() => {
    let animationFrame: number;

    const handleMouseMove = (e: MouseEvent) => {
      const newPosition = { x: e.clientX, y: e.clientY };
      setMousePosition(newPosition);

      // Add trail point
      const newTrailPoint = {
        x: newPosition.x,
        y: newPosition.y,
        id: trailIdRef.current++
      };

      setTrail(prevTrail => {
        const newTrail = [newTrailPoint, ...prevTrail.slice(0, 8)]; // Keep last 8 points
        return newTrail;
      });
    };

    const handleMouseDown = () => setIsClicking(true);
    const handleMouseUp = () => setIsClicking(false);

    const handleMouseEnter = () => setIsHovering(true);
    const handleMouseLeave = () => setIsHovering(false);

    // Add event listeners for interactive elements
    const updateInteractiveElements = () => {
      const interactiveElements = document.querySelectorAll('a, button, [role="button"], input, textarea, select');

      interactiveElements.forEach(el => {
        el.addEventListener('mouseenter', handleMouseEnter);
        el.addEventListener('mouseleave', handleMouseLeave);
      });

      return interactiveElements;
    };

    const interactiveElements = updateInteractiveElements();

    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('mousedown', handleMouseDown);
    window.addEventListener('mouseup', handleMouseUp);

    // Cleanup trail periodically
    const trailCleanup = setInterval(() => {
      setTrail(prevTrail => prevTrail.slice(0, 6));
    }, 100);

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mousedown', handleMouseDown);
      window.removeEventListener('mouseup', handleMouseUp);
      clearInterval(trailCleanup);
      if (animationFrame) cancelAnimationFrame(animationFrame);

      interactiveElements.forEach(el => {
        el.removeEventListener('mouseenter', handleMouseEnter);
        el.removeEventListener('mouseleave', handleMouseLeave);
      });
    };
  }, []);

  return (
    <>
      {/* Cursor Trail */}
      {trail.map((point, index) => (
        <div
          key={point.id}
          className="cursor-trail-point hidden md:block"
          style={{
            left: point.x - 3,
            top: point.y - 3,
            opacity: (trail.length - index) / trail.length * 0.6,
            transform: `scale(${(trail.length - index) / trail.length * 0.8})`,
          }}
        />
      ))}

      {/* Main cursor glow ring */}
      <div
        className={`cursor-glow hidden md:block transition-all duration-300 ease-out ${
          isHovering ? 'scale-150 opacity-80' : 'scale-100 opacity-60'
        } ${isClicking ? 'scale-75' : ''}`}
        style={{
          left: mousePosition.x - 20,
          top: mousePosition.y - 20,
        }}
      />

      {/* Interactive hover ring */}
      {isHovering && (
        <div
          className="cursor-hover-ring hidden md:block"
          style={{
            left: mousePosition.x - 15,
            top: mousePosition.y - 15,
          }}
        />
      )}

      {/* Cursor particles */}
      {isClicking && (
        <>
          {[...Array(6)].map((_, i) => {
            const angle = (i * 60) * (Math.PI / 180); // Convert to radians
            const distance = 20;
            const offsetX = Math.cos(angle) * distance;
            const offsetY = Math.sin(angle) * distance;

            return (
              <div
                key={i}
                className="cursor-particle hidden md:block"
                style={{
                  left: mousePosition.x - 2,
                  top: mousePosition.y - 2,
                  animationDelay: `${i * 0.05}s`,
                  '--offset-x': `${offsetX}px`,
                  '--offset-y': `${offsetY}px`,
                } as React.CSSProperties}
              />
            );
          })}
        </>
      )}
    </>
  );
};

export default CustomCursor;
