import { Code2, Linkedin, Mail, ExternalLink } from 'lucide-react';
import GlassCard from './GlassCard';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const socialLinks = [
    {
      name: 'GitHub',
      icon: Code2,
      href: 'https://github.com/baraa-alkhateeb',
      color: 'hover:text-white'
    },
    {
      name: 'LinkedIn',
      icon: Linkedin,
      href: 'https://linkedin.com/in/baraa-alkhateeb',
      color: 'hover:text-blue-400'
    },
    {
      name: 'Email',
      icon: Mail,
      href: 'mailto:<EMAIL>',
      color: 'hover:text-orange-400'
    }
  ];

  const quickLinks = [
    { name: 'About', href: '#about' },
    { name: 'Projects', href: '#projects' },
    { name: 'Services', href: '#services' },
    { name: 'Contact', href: '#contact' }
  ];

  return (
    <footer className="relative py-16 px-4 sm:px-6 bg-gradient-to-t from-black via-slate-950 to-slate-900">
      <div className="max-w-6xl mx-auto">
        <GlassCard className="border-white/5 p-8 sm:p-12">
          <div className="grid md:grid-cols-3 gap-8 items-start">
            
            {/* Brand & Description */}
            <div className="md:col-span-1">
              <div className="mb-6">
                <h3 className="text-2xl font-light text-white mb-3">
                  Baraa Al Khateeb
                </h3>
                <p className="text-slate-400 text-sm leading-relaxed font-light">
                  Crafting digital experiences with precision, innovation, and a passion for excellence.
                </p>
              </div>
              
              {/* Social Links */}
              <div className="flex gap-4">
                {socialLinks.map((link) => {
                  const Icon = link.icon;
                  return (
                    <a
                      key={link.name}
                      href={link.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={`w-10 h-10 bg-white/5 border border-white/10 rounded-lg flex items-center justify-center text-white/60 ${link.color} transition-all duration-300 hover:bg-white/10 hover:border-white/20 hover-lift group`}
                      aria-label={link.name}
                    >
                      <Icon className="w-4 h-4 group-hover:scale-110 transition-transform duration-300" />
                    </a>
                  );
                })}
              </div>
            </div>

            {/* Quick Links */}
            <div className="md:col-span-1">
              <h4 className="text-lg font-light text-white mb-6">Quick Links</h4>
              <nav className="space-y-3">
                {quickLinks.map((link) => (
                  <a
                    key={link.name}
                    href={link.href}
                    className="block text-slate-400 hover:text-white transition-colors duration-300 text-sm font-light group"
                  >
                    <span className="flex items-center gap-2">
                      {link.name}
                      <ExternalLink className="w-3 h-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    </span>
                  </a>
                ))}
              </nav>
            </div>

            {/* Contact Info */}
            <div className="md:col-span-1">
              <h4 className="text-lg font-light text-white mb-6">Get In Touch</h4>
              <div className="space-y-4">
                <div className="text-sm">
                  <p className="text-slate-500 font-light mb-1">Email</p>
                  <a 
                    href="mailto:<EMAIL>" 
                    className="text-slate-300 hover:text-orange-400 transition-colors duration-300 font-light"
                  >
                    <EMAIL>
                  </a>
                </div>
                <div className="text-sm">
                  <p className="text-slate-500 font-light mb-1">Location</p>
                  <p className="text-slate-300 font-light">Available Worldwide</p>
                </div>
                <div className="text-sm">
                  <p className="text-slate-500 font-light mb-1">Status</p>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                    <span className="text-green-400 font-light">Available for projects</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom Bar */}
          <div className="mt-12 pt-8 border-t border-white/5">
            <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
              <p className="text-slate-500 text-sm font-light">
                © {currentYear} Baraa Al Khateeb. Crafted with passion and precision.
              </p>
              <div className="flex items-center gap-6 text-xs text-slate-500">
                <a href="#" className="hover:text-slate-300 transition-colors duration-300 font-light">
                  Privacy Policy
                </a>
                <a href="#" className="hover:text-slate-300 transition-colors duration-300 font-light">
                  Terms of Service
                </a>
              </div>
            </div>
          </div>
        </GlassCard>

        {/* Ambient Effects */}
        <div className="absolute bottom-0 left-1/4 w-64 h-32 bg-gradient-to-t from-orange-500/[0.02] to-transparent rounded-full filter blur-3xl" />
        <div className="absolute bottom-0 right-1/4 w-48 h-24 bg-gradient-to-t from-blue-500/[0.01] to-transparent rounded-full filter blur-3xl" />
      </div>
    </footer>
  );
};

export default Footer;
