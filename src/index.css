@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    font-weight: 300;
    overflow-x: hidden;
  }

  html {
    scroll-behavior: smooth;
    font-size: 16px;
  }

  /* Enhanced Cursor Effects */
  .cursor-glow {
    position: fixed;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    background: radial-gradient(circle, rgba(249, 115, 22, 0.15) 0%, rgba(249, 115, 22, 0.05) 50%, transparent 100%);
    border: 1px solid rgba(249, 115, 22, 0.2);
  }

  .cursor-hover-ring {
    position: fixed;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    pointer-events: none;
    z-index: 9998;
    border: 2px solid rgba(255, 255, 255, 0.4);
    animation: cursor-pulse 1.5s ease-in-out infinite;
  }

  .cursor-trail-point {
    position: fixed;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    pointer-events: none;
    z-index: 9997;
    background: rgba(249, 115, 22, 0.6);
    transition: all 0.1s ease-out;
  }

  .cursor-particle {
    position: fixed;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    pointer-events: none;
    z-index: 9996;
    background: rgba(249, 115, 22, 0.8);
    animation: cursor-particle-burst 0.6s ease-out forwards;
  }

  @keyframes cursor-pulse {
    0%, 100% {
      transform: scale(1);
      opacity: 0.6;
    }
    50% {
      transform: scale(1.2);
      opacity: 0.3;
    }
  }

  @keyframes cursor-particle-burst {
    0% {
      transform: translate(0, 0) scale(1);
      opacity: 1;
    }
    100% {
      transform: translate(var(--offset-x, 20px), var(--offset-y, 0px)) scale(0);
      opacity: 0;
    }
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
  }
  
  ::-webkit-scrollbar-track {
    background: rgba(15, 23, 42, 0.2);
    border-radius: 3px;
  }
  
  ::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, rgba(249, 115, 22, 0.6), rgba(255, 255, 255, 0.3));
    border-radius: 3px;
    transition: background 0.2s ease;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, rgba(249, 115, 22, 0.8), rgba(255, 255, 255, 0.5));
  }

  /* Firefox scrollbar */
  html {
    scrollbar-width: thin;
    scrollbar-color: rgba(249, 115, 22, 0.6) rgba(15, 23, 42, 0.2);
  }
}

@layer components {
  .glass-morphism {
    background: rgba(15, 23, 42, 0.3);
    backdrop-filter: blur(24px);
    border: 1px solid rgba(255, 255, 255, 0.06);
    box-shadow: 
      0 8px 32px 0 rgba(0, 0, 0, 0.18),
      inset 0 1px 0 rgba(255, 255, 255, 0.05);
  }

  .glass-effect {
    background: linear-gradient(135deg, 
      rgba(255, 255, 255, 0.04), 
      rgba(255, 255, 255, 0.01)
    );
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.06);
    box-shadow: 
      0 8px 32px 0 rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.05);
  }

  .text-gradient {
    background: linear-gradient(135deg, #ffffff, #e5e7eb);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .orange-accent {
    background: linear-gradient(135deg, rgba(249, 115, 22, 0.1), transparent);
  }

  /* Enhanced animations */
  @keyframes float-slow {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-15px) rotate(2deg); }
  }

  @keyframes fade-in {
    0% { opacity: 0; transform: scale(0.9); }
    100% { opacity: 1; transform: scale(1); }
  }

  @keyframes preloader-float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  @keyframes glow-pulse {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.6; }
  }

  @keyframes slide-in-from-left {
    0% { 
      opacity: 0; 
      transform: translateX(-30px) translateY(10px); 
    }
    100% { 
      opacity: 1; 
      transform: translateX(0) translateY(0); 
    }
  }

  @keyframes orange-glow {
    0%, 100% {
      box-shadow: 0 0 20px rgba(249, 115, 22, 0.1);
    }
    50% {
      box-shadow: 0 0 30px rgba(249, 115, 22, 0.2);
    }
  }

  /* Mobile navigation animations */
  @keyframes mobile-dropdown-enter {
    from {
      opacity: 0;
      transform: scaleY(0.95) translateY(-8px);
    }
    to {
      opacity: 1;
      transform: scaleY(1) translateY(0);
    }
  }

  @keyframes mobile-dropdown-exit {
    from {
      opacity: 1;
      transform: scaleY(1) translateY(0);
    }
    to {
      opacity: 0;
      transform: scaleY(0.95) translateY(-8px);
    }
  }

  @keyframes mobile-nav-item-enter {
    from {
      opacity: 0;
      transform: translateY(8px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes mobile-nav-item-exit {
    from {
      opacity: 1;
      transform: translateY(0);
    }
    to {
      opacity: 0;
      transform: translateY(-8px);
    }
  }

  .animate-float-slow {
    animation: float-slow 8s ease-in-out infinite;
  }

  .animate-glow-pulse {
    animation: glow-pulse 3s ease-in-out infinite;
  }

  .animate-slide-in-left {
    animation: slide-in-from-left 0.8s ease-out forwards;
  }

  .animate-orange-glow {
    animation: orange-glow 3s ease-in-out infinite;
  }

  .animate-fade-in {
    animation: fade-in 0.6s ease-out forwards;
  }

  .animate-preloader-float {
    animation: preloader-float 2s ease-in-out infinite;
  }

  /* Navigation indicator animations */
  .nav-indicator {
    will-change: transform, opacity;
    backface-visibility: hidden;
    transform: translateZ(0);
  }

  .nav-item {
    will-change: color, background-color;
    backface-visibility: hidden;
  }

  /* Hover animations - shorter and snappier */
  .hover-lift {
    transition: all 0.15s ease-out;
  }

  .hover-lift:hover {
    transform: translateY(-2px);
  }

  .hover-scale {
    transition: all 0.15s ease-out;
  }

  .hover-scale:hover {
    /* Scale effect removed to prevent interference with interactive elements */
  }

  .hover-glow {
    transition: all 0.15s ease-out;
  }

  .hover-glow:hover {
    box-shadow: 0 8px 25px rgba(249, 115, 22, 0.15);
  }
}

/* Selection styles */
::selection {
  background-color: rgba(249, 115, 22, 0.2);
  color: white;
}

::-moz-selection {
  background-color: rgba(249, 115, 22, 0.2);
  color: white;
}

/* Focus and accessibility improvements */
input:focus-visible,
textarea:focus-visible,
button:focus-visible,
a:focus-visible {
  outline: 2px solid rgba(249, 115, 22, 0.6) !important;
  outline-offset: 2px !important;
}

/* Ensure interactive elements are properly layered */
input,
textarea,
button,
a {
  position: relative;
  z-index: 10;
}

/* Mobile Navigation Improvements */
@media (max-width: 768px) {
  /* Ensure mobile navigation is properly layered */
  nav {
    z-index: 9999 !important;
  }

  /* Improve touch targets on mobile */
  nav button,
  nav a {
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Mobile navigation dropdown animations */
  .mobile-nav-dropdown {
    animation: mobile-dropdown-enter 0.3s ease-out;
  }

  .mobile-nav-dropdown.closing {
    animation: mobile-dropdown-exit 0.2s ease-in;
  }

  /* Mobile navigation item stagger animation */
  .mobile-nav-item {
    animation: mobile-nav-item-enter 0.4s ease-out both;
  }

  /* Ensure smooth dropdown positioning */
  .mobile-nav-dropdown {
    max-height: calc(100vh - var(--nav-height, 80px) - 2rem);
    overflow-y: auto;
  }

  /* Enhanced backdrop for dropdown */
  .mobile-nav-backdrop {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }
}

/* Responsive utilities */
@media (max-width: 767px) {
  .cursor-glow,
  .cursor-hover-ring,
  .cursor-trail-point,
  .cursor-particle {
    display: none !important;
  }

  /* Additional mobile navigation fixes for very small screens */
  nav {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  /* Ensure mobile overlay covers entire screen */
  .mobile-nav-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    height: 100dvh !important; /* Dynamic viewport height for mobile browsers */
  }

  /* Optimize mobile panel for small screens */
  .mobile-nav-panel {
    max-width: 100vw !important;
  }
}

/* Additional cursor effect hiding for very small screens */
@media (max-width: 640px) {
  .cursor-glow,
  .cursor-hover-ring,
  .cursor-trail-point,
  .cursor-particle {
    display: none !important;
  }
}
